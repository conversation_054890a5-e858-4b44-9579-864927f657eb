import { useEffect } from "react";
import { Dropdown } from "primereact/dropdown";

import { useDataTableContext } from '@contexts/DataTableContext';
import { useQueryClient } from "react-query";
import { Controller, useForm } from "react-hook-form";

const TemplateImageHeader = () => {
    const { control, reset } = useForm()
    const { setLazyParams, lazyParams } = useDataTableContext()

    const queryClient = useQueryClient();
    const cachedDesigns = queryClient.getQueryData("getDesigns");

    const onchange = (val) => {
        setLazyParams(prev => ({ ...prev, designID: val }))
    }

    useEffect(() => {
        if (lazyParams?.designID) {
            reset({ design_id: Number(lazyParams.designID) })
        }
    }, [lazyParams?.designID, reset])

    return (
        <Controller name="design_id" control={control}
            rules={{ required: false }}
            render={({ field }) => {
                return (
                    <Dropdown
                        id={field.name} {...field}
                        value={field.value}
                        options={cachedDesigns}
                        onChange={(e) => {
                            field.onChange(e.value);
                            onchange(e.value)
                        }}
                        optionLabel="name"
                        optionValue="id"
                        inputRef={field.ref}
                        filter showClear filterBy="name"
                        placeholder="select design"
                        className='rounded-[6px] me-3 text-[black]'
                    />
                )
            }
            } />
    )
}

export default TemplateImageHeader