{"name": "abanda-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@stripe/stripe-js": "^6.0.0", "axios": "^1.7.7", "chart.js": "^4.4.9", "clsx": "^2.1.1", "file-saver": "^2.0.5", "framer-motion": "^12.12.1", "html-react-parser": "^5.2.1", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "lodash": "^4.17.21", "path": "^0.12.7", "primeicons": "^6.0.1", "primereact": "^8.7.3", "prop-types": "^15.8.1", "react": "^18.3.1", "react-alignment-guides": "^1.0.8", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.0", "react-i18next": "^14.1.2", "react-icons": "^5.2.1", "react-query": "^3.39.3", "react-router-dom": "^6.23.1", "react-scripts": "^5.0.1", "react-toastify": "^11.0.5", "uuid": "^11.0.3"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^6.2.2"}}