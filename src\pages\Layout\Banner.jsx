import React, { useRef } from 'react'

import defaultImage from "@images/employee.svg"
import { Image } from 'primereact/image';
import { Menu } from 'primereact/menu';

import { MdKeyboardArrowDown } from "react-icons/md";
// import { CgProfile } from "react-icons/cg";
import { RiBillLine } from "react-icons/ri";
import { LuLogOut } from "react-icons/lu";
import { FiSettings } from 'react-icons/fi';
import { useLogoutMutation } from '@quires/auth';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useNavigate } from 'react-router-dom';


function Banner() {
    const {   userType } = useGlobalContext();
    const navigate = useNavigate();

    const logout = useLogoutMutation()

    const menu = useRef(null);
    const items = [
        {
            label: 'Options',
            items: [
                {
                    label: 'Settings',
                    icon: <FiSettings className='me-2' />,
                    disabled: (localStorage.getItem("user_role") == "admin"),
                    command: () => {navigate('/manager/settings');

                    }
                },
                {
                    label: 'Billing',
                    icon: <RiBillLine className='me-2' />,
                    disabled: (localStorage.getItem("user_role") == "admin"),
                    command: () => {
                        const prefix = userType === 'admin' ? '/admin' : '/manager';  //Keep it incase I need to give admin a setting page
                        navigate(`${prefix}/billing`);
                    }
                },
                {
                    label: 'Logout',
                    icon: <LuLogOut className='me-2' />,
                    command: async () => {
                        await logout.mutateAsync()
                    }
                }
            ]
        }
    ];

    return (
        <nav className='w-full flex  bg-[white]'>                       {/*If you wish to return it to normal navbar functionallity add these (justify-end p-5) to the class*/}
            <Menu model={items} popup ref={menu} id="popup_menu" />

            <div className='flex items-start'>
                <Image src={defaultImage} alt="profile image" imageClassName="rounded-full" width="40" heigh="40" />
                <button
                    onClick={(event) => menu.current.toggle(event)}
                    aria-controls="popup_menu"
                    aria-haspopup
                    className='flex flex-col mx-2'>
                    <div className='text-md font-bold flex items-center'>
                        <h5 className='me-2 capitalize'>{localStorage.getItem("user_name") || "User name"} </h5>
                        <MdKeyboardArrowDown size={18} />
                    </div>
                    <small className='ms-1 capitalize'>{userType}</small>
                </button>

            </div>
        </nav>
    )
}

export default Banner