/* Override styles to remove transparency from design space */

.design-space {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important;
  transform-origin: center center !important;
  will-change: transform;
 
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Design space content - ensure uniform background */
#design-space-content {
  
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
  background-image: none !important;
  background-attachment: fixed !important;
  background-size: auto !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Remove any gradient or blur effects from design space container */
.design-space-container {
  position: relative;
  z-index: 5;
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Hide design elements from the design area */
.design-elements-container {
  z-index: 1;
  pointer-events: none;
}

/* Smart positioning for element controls */
.draggable-element.top-edge .element-controls {
  top: auto !important;
  bottom: -55px !important;
}

.draggable-element.top-edge .element-controls::after {
  bottom: auto !important;
  top: -6px !important;
}

.draggable-element.right-edge .element-controls {
  right: auto !important;
  left: -10px !important;
}

.draggable-element.right-edge .element-controls::after {
  right: auto !important;
  left: 15px !important;
}

/* Remove any background effects from the main design space area */
.design-space > div {
  opacity: 1 !important;
 
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Ensure zoom container allows overflow for zoomed content */
.design-space-container {
  overflow: auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 100% !important;
  padding: 50px !important;
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Keep shadow but make it more solid */

/* Remove hover effects that change transparency */

/* Extra force: Remove any scale/transform from libraries (framer-motion, Tailwind, etc) on hover when zoom is not 100% */
.design-space.no-hover-zoom:hover, .design-space.no-hover-zoom.motion-hover, .design-space.no-hover-zoom:hover, .design-space.no-hover-zoom:active {
  transform: none !important;
  scale: 1 !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: none !important;
  filter: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  
}

/* Make corner marks more visible */
.design-space .corner-mark {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* Remove forced white background */
.design-space > div {
  opacity: 1 !important;
}

/* Zoom functionality styles */
.design-space[data-design-space="true"] {
  transform-origin: center center !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
}

/* Smooth zoom transitions */
.design-space.zooming {
  transition: transform 0.3s ease !important;
}

/* ===== TOOLBAR STYLING - SEPARATED FROM DESIGN SPACE ===== */

/* Animation for toolbar appearance */
@keyframes toolbarSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(0.9) translateY(0);
  }
}

/* Element controls - completely separate from design space styling */
.element-controls {
  transform: scale(0.7);
  gap: 2px;
  font-size: 10px;
  padding: 3px 6px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 10px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important;
  position: absolute !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: toolbarSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.element-controls:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 6px 20px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 50%, #34495e 100%) !important;
}

.element-control-btn {
  width: 24px !important;
  height: 24px !important;
  min-width: 0 !important;
  min-height: 0 !important;
  font-size: 10px !important;
  padding: 0 !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  color: #ffffff !important;
  position: relative !important;
  overflow: hidden !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.element-control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.element-control-btn:hover::before {
  left: 100%;
}

.element-control-btn:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.8) 0%, rgba(6, 182, 212, 0.6) 100%) !important;
  color: #ffffff !important;
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4), 0 2px 8px rgba(6, 182, 212, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(6, 182, 212, 0.8) !important;
}

.element-control-btn:active {
  transform: translateY(0) scale(0.98) !important;
  transition: all 0.1s ease !important;
}

.element-controls .element-control-btn:hover, 
.element-controls .element-control-btn:focus {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.9) 0%, rgba(6, 182, 212, 0.7) 100%) !important;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.5), 0 4px 12px rgba(6, 182, 212, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(6, 182, 212, 1) !important;
}

/* Enhanced button icons */
.element-control-btn svg {
  width: 14px !important;
  height: 14px !important;
  transition: all 0.25s ease !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
}

.element-control-btn:hover svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4)) !important;
  transform: scale(1.1) !important;
}

/* Special styling for different button types */
.element-control-btn.rotate-btn:hover {
  background: linear-gradient(135deg, rgba(139, 61, 255, 0.8) 0%, rgba(139, 61, 255, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(139, 61, 255, 0.4), 0 2px 8px rgba(139, 61, 255, 0.3) !important;
  border-color: rgba(139, 61, 255, 0.8) !important;
}

.element-control-btn.delete-btn:hover {
  background: linear-gradient(135deg, rgba(255, 92, 92, 0.8) 0%, rgba(255, 92, 92, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(255, 92, 92, 0.4), 0 2px 8px rgba(255, 92, 92, 0.3) !important;
  border-color: rgba(255, 92, 92, 0.8) !important;
}

.element-control-btn.duplicate-btn:hover {
  background: linear-gradient(135deg, rgba(0, 196, 140, 0.8) 0%, rgba(0, 196, 140, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(0, 196, 140, 0.4), 0 2px 8px rgba(0, 196, 140, 0.3) !important;
  border-color: rgba(0, 196, 140, 0.8) !important;
}

/* Glow effect for active buttons */
.element-control-btn.active-mode {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.9) 0%, rgba(6, 182, 212, 0.7) 100%) !important;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.6), 0 4px 12px rgba(6, 182, 212, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(6, 182, 212, 1) !important;
}

/* Ensure toolbar doesn't inherit design space styles */
.draggable-element .element-controls,
.draggable-element .element-controls *,
.element-controls,
.element-controls * {
  background: initial !important;
  backdrop-filter: initial !important;
  -webkit-backdrop-filter: initial !important;
  filter: initial !important;
}

/* Force toolbar to maintain dark background matching triangle pointer */
.draggable-element .element-controls,
.element-controls {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
  background-image: none !important;
  background-attachment: fixed !important;
  background-size: auto !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Override any white background that might be applied */
.element-controls,
.element-controls *,
.draggable-element .element-controls,
.draggable-element .element-controls * {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
}

/* Ensure toolbar elements don't inherit any background from parent elements */
.draggable-element .element-controls *,
.element-controls * {
  background: initial !important;
  backdrop-filter: initial !important;
  -webkit-backdrop-filter: initial !important;
  filter: initial !important;
}

/* But force the main toolbar to keep its dark background */
.element-controls {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
}

/* Arrow position for default (top) - HIDDEN */
.element-controls::after {
  display: none !important;
}

/* Arrow position for top edge (when toolbar is at bottom) - HIDDEN */
.element-controls.top-edge::after {
  display: none !important;
}

/* Arrow position for right edge - HIDDEN */
.element-controls.right-edge::after {
  display: none !important;
}

/* Arrow position for left edge - HIDDEN */
.element-controls.left-edge::after {
  display: none !important;
}

/* Arrow position for both top and right edges - HIDDEN */
.element-controls.top-edge.right-edge::after {
  display: none !important;
}

/* Arrow position for both top and left edges - HIDDEN */
.element-controls.top-edge.left-edge::after {
  display: none !important;
}

/* Fix transparency of ColorPicker dropdown menu */
.color-picker-btn + .absolute,
.color-picker-dropdown {
  background: #fff;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  filter: none;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18), 0 2px 8px rgba(0,0,0,0.10);
  opacity: 1;
}

/* Radical fix for color boxes in ColorPicker - allows inline style to appear */
.color-picker-dropdown button[style][title]:not([type]),
.color-picker-dropdown .grid button[style]:not([type]) {
  background: unset !important;
  background-color: unset !important;
  box-shadow: 0 1px 4px rgba(0,0,0,0.10);
  border-radius: 4px;
  border: 1px solid #eee;
  opacity: 1 !important;
  min-width: 0;
  min-height: 0;
  padding: 0;
}

/* --- FIX: Allow color swatches to show their real color in TextSettings.jsx --- */

/* .color-swatch, .w-8.h-8.rounded-lg.border-2, .w-10.h-10.rounded-lg.border-2 {
  background: none !important;
  background-color: inherit !important;
} */

/* button[style*="background-color"] {
  background: none !important;
  background-color: unset !important;
}

button[style*="background-color"] {
  background-color: inherit !important;
} */

/* .color-picker-dropdown button, .color-picker-dropdown .grid button, .color-picker-dropdown .color-swatch, .color-picker-dropdown [class*="w-8 h-8"], .color-picker-dropdown [class*="w-10 h-10"] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: var(--actual-color) !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style] {
  background: transparent !important;
  background-color: currentColor !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color:"] {
  background: transparent !important;
  background-color: inherit !important;
  background-image: none !important;
} */

/* .color-picker-dropdown * {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: var(--swatch-color, inherit) !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style] {
  background: transparent !important;
  background-color: attr(style background-color) !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: unset !important;
  background-image: none !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
} */

/* .color-picker-dropdown button[style*="background-color"] {
  background-color: inherit !important;
  background: inherit !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[data-color] {
  background-color: var(--data-color) !important;
  background: var(--data-color) !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color"] {
  background-color: var(--extracted-color) !important;
  background: var(--extracted-color) !important;
  background-image: none !important;
} */

/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* --- END FIX --- */

/* Exclude color buttons inside ColorPicker from the dark background imposed by element-controls */
.element-controls .color-picker-dropdown button[style][title] {
  background: none !important;
  background-color: inherit !important;
}

/* Exclude color boxes from the dark background imposed by element-controls */
.element-controls .color-picker-dropdown .color-swatch {
  background: none !important;
  background-color: inherit !important;
}

/* Try to disable any effect or conflict and force the color to appear in color boxes */
.element-controls .color-picker-dropdown .color-swatch,
.color-swatch[style] {
  background: none !important;
  background-color: unset !important;
  background-blend-mode: normal !important;
  mix-blend-mode: normal !important;
  isolation: isolate !important;
  z-index: 9999 !important;
  opacity: 1 !important;
}

/* Force the color of the box from a CSS variable and cancel any other effect */
.color-swatch {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  border: 2px solid #ccc;
  background-color: var(--swatch-color, #eee);
  box-shadow: 0 1px 4px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: transform 0.15s, border-color 0.15s;
  opacity: 1;
}

.color-swatch:hover {
  transform: scale(1.1);
  border-color: #888;
}

.color-swatch.selected {
  border-color: #222;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
}

/* White background for the color dropdown menu */
.color-picker-dropdown,
.absolute.top-full.left-0.mt-2.w-80.bg-white {
  background: #fff !important;
  border-radius: 1rem !important;
  border: 1px solid #eee !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18), 0 2px 8px rgba(0,0,0,0.10) !important;
  z-index: 99999 !important;
  opacity: 1 !important;
}

/* Force the real background color to appear in color boxes */
/* .color-swatch, .w-8.h-8.rounded-lg.border-2, .w-10.h-10.rounded-lg.border-2 {
  background: none !important;
  background-color: inherit !important;
} */

/* Force the color from the inline style */
/* button[style*="background-color"] {
  background: none !important;
  background-color: unset !important;
}

button[style*="background-color"] {
  background-color: inherit !important;
} */

/* ===== New fix: Force the real colors to appear in the boxes ===== */

/* Cancel any inherited background from the white menu */
/* .color-picker-dropdown button, .color-picker-dropdown .grid button, .color-picker-dropdown .color-swatch, .color-picker-dropdown [class*="w-8 h-8"], .color-picker-dropdown [class*="w-10 h-10"] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
} */

/* Force reading the color from the inline style */
/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: var(--actual-color) !important;
  background-image: none !important;
} */

/* Extract the color from the inline style and reapply it */
/* .color-picker-dropdown button[style] {
  background: transparent !important;
  background-color: currentColor !important;
  background-image: none !important;
} */

/* Force applying the color specified in the style attribute */
/* .color-picker-dropdown button[style*="background-color:"] {
  background: transparent !important;
  background-color: inherit !important;
  background-image: none !important;
} */

/* Cancel any effect from the white background of the menu on the boxes */
/* .color-picker-dropdown * {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
} */

/* Reapply the real colors to the boxes */
/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: var(--swatch-color, inherit) !important;
  background-image: none !important;
} */

/* Force reading the color from the style attribute directly */
/* .color-picker-dropdown button[style] {
  background: transparent !important;
  background-color: attr(style background-color) !important;
  background-image: none !important;
} */

/* Final solution: Force the color from the inline style without any other effects */
/* .color-picker-dropdown button[style*="background-color"] {
  background: transparent !important;
  background-color: unset !important;
  background-image: none !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
} */

/* Force applying the color specified in the style attribute */
/* .color-picker-dropdown button[style*="background-color"] {
  background-color: inherit !important;
  background: inherit !important;
  background-image: none !important;
} */

/* ===== Solution using data attributes to force color appearance ===== */

/* Force the color from the data-color attribute */
/* .color-picker-dropdown button[data-color] {
  background-color: var(--data-color) !important;
  background: var(--data-color) !important;
  background-image: none !important;
} */

/* Force applying the color from the style attribute directly */
/* .color-picker-dropdown button[style*="background-color"] {
  background-color: var(--extracted-color) !important;
  background: var(--extracted-color) !important;
  background-image: none !important;
} */

/* Cancel any effect from the white background of the menu on the boxes */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* Force reapplying the color after canceling the background */
/* .color-picker-dropdown button[style*="background-color"]::before, .color-picker-dropdown button[data-color]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: inherit !important;
  background: inherit !important;
  border-radius: inherit;
  z-index: -1;
} */

/* Final solution: Force color using CSS custom properties */
/* .color-picker-dropdown button[style*="background-color"] {
  --forced-color: attr(style background-color);
  background-color: var(--forced-color) !important;
  background: var(--forced-color) !important;
} */

/* ===== Solution using pseudo-elements to force color appearance ===== */

/* Create a background layer to show the real color */
/* .color-picker-dropdown button[style*="background-color"]::after, .color-picker-dropdown button[data-color]::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background-color: var(--data-color, inherit) !important;
  background: var(--data-color, inherit) !important;
  border-radius: calc(0.5rem - 2px);
  z-index: 1;
  pointer-events: none;
} */

/* Force the color from the style attribute in the pseudo-element */
/* .color-picker-dropdown button[style*="background-color"]::after {
  background-color: var(--extracted-color, inherit) !important;
  background: var(--extracted-color, inherit) !important;
} */

/* Cancel any background from the original element */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color] {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  position: relative !important;
} */

/* Force color in the pseudo-element using CSS custom properties */
/* .color-picker-dropdown button[style*="background-color"]::after {
  --swatch-color: var(--extracted-color, inherit);
  background-color: var(--swatch-color) !important;
  background: var(--swatch-color) !important;
} */

/* Final solution: Use CSS Grid to force color */
/* .color-picker-dropdown button[style*="background-color"] {
  display: grid !important;
  grid-template: 1fr / 1fr !important;
} */

/* .color-picker-dropdown button[style*="background-color"]::before {
  content: '';
  grid-area: 1 / 1;
  background-color: var(--extracted-color, inherit) !important;
  background: var(--extracted-color, inherit) !important;
  border-radius: inherit;
  z-index: 1;
} */

/* ===== Solution using CSS Variables to force color appearance ===== */

/* Force the color from CSS custom properties */
/* .color-picker-dropdown button[data-color] {
  --swatch-color: var(--data-color);
  background-color: var(--swatch-color) !important;
  background: var(--swatch-color) !important;
  background-image: none !important;
} */

/* Force the color from the style attribute in CSS variable */
/* .color-picker-dropdown button[style*="background-color"] {
  --swatch-color: var(--extracted-color);
  background-color: var(--swatch-color) !important;
  background: var(--swatch-color) !important;
  background-image: none !important;
} */

/* Cancel any effect from the white background */
/* .color-picker-dropdown {
  --dropdown-bg: #fff;
  background: var(--dropdown-bg) !important;
} */

/* Force boxes not to inherit the menu background */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color] {
  background: var(--swatch-color) !important;
  background-color: var(--swatch-color) !important;
  background-image: none !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
} */

/* Final solution: Use CSS layers to force color */
/* @layer color-swatches {
  .color-picker-dropdown button[style*="background-color"] {
    background-color: var(--extracted-color) !important;
    background: var(--extracted-color) !important;
  }
  
  .color-picker-dropdown button[data-color] {
    background-color: var(--data-color) !important;
    background: var(--data-color) !important;
  }
} */

/* ===== Final solution: Most specific to force color appearance ===== */

/* Force color using the most specific selectors */
/* .color-picker-dropdown .grid button[style*="background-color"], .color-picker-dropdown button[style*="background-color"][title], .color-picker-dropdown button[data-color][title] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
  background-attachment: scroll !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
} */

/* Force color in all states */
/* .color-picker-dropdown button[style*="background-color"]:hover, .color-picker-dropdown button[style*="background-color"]:focus, .color-picker-dropdown button[style*="background-color"]:active, .color-picker-dropdown button[data-color]:hover, .color-picker-dropdown button[data-color]:focus, .color-picker-dropdown button[data-color]:active {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in pseudo-elements */
/* .color-picker-dropdown button[style*="background-color"]::before, .color-picker-dropdown button[style*="background-color"]::after, .color-picker-dropdown button[data-color]::before, .color-picker-dropdown button[data-color]::after {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Final solution: Force color in all child elements */
/* .color-picker-dropdown button[style*="background-color"] *, .color-picker-dropdown button[data-color] * {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* ===== Final solution: Browser support and force color appearance ===== */

/* Support CSS custom properties */
/* @supports (--custom-property: value) {
  .color-picker-dropdown button[style*="background-color"] {
    --swatch-color: var(--extracted-color);
    background-color: var(--swatch-color) !important;
    background: var(--swatch-color) !important;
  }
  
  .color-picker-dropdown button[data-color] {
    --swatch-color: var(--data-color);
    background-color: var(--swatch-color) !important;
    background: var(--swatch-color) !important;
  }
} */

/* Support CSS Grid */
/* @supports (display: grid) {
  .color-picker-dropdown button[style*="background-color"] {
    display: grid !important;
    grid-template: 1fr / 1fr !important;
  }
  
  .color-picker-dropdown button[style*="background-color"]::before {
    content: '';
    grid-area: 1 / 1;
    background-color: var(--extracted-color) !important;
    background: var(--extracted-color) !important;
    border-radius: inherit;
    z-index: 1;
  }
} */

/* Support CSS Layers */
/* @supports (layer: color-swatches) {
  @layer color-swatches {
    .color-picker-dropdown button[style*="background-color"] {
      background-color: var(--extracted-color) !important;
      background: var(--extracted-color) !important;
    }
  }
} */

/* Final solution: Force color in all browsers */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
  background-attachment: scroll !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* ===== Final solution: Highest specificity to force color appearance ===== */

/* Highest specificity to force color */
/* .color-picker-dropdown.color-picker-dropdown.color-picker-dropdown button[style*="background-color"], .color-picker-dropdown.color-picker-dropdown.color-picker-dropdown button[data-color] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states */
/* .color-picker-dropdown button[style*="background-color"]:not(:hover):not(:focus):not(:active), .color-picker-dropdown button[data-color]:not(:hover):not(:focus):not(:active) {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with hover */
/* .color-picker-dropdown button[style*="background-color"]:hover, .color-picker-dropdown button[data-color]:hover {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with focus */
/* .color-picker-dropdown button[style*="background-color"]:focus, .color-picker-dropdown button[data-color]:focus {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with active */
/* .color-picker-dropdown button[style*="background-color"]:active, .color-picker-dropdown button[data-color]:active {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Final solution: Force color in all elements */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color], .color-picker-dropdown .grid button[style*="background-color"], .color-picker-dropdown .grid button[data-color] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
  background-attachment: scroll !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* ===== Final solution: Highest specificity possible ===== */

/* Highest specificity possible to force color */
/* .color-picker-dropdown.color-picker-dropdown.color-picker-dropdown.color-picker-dropdown button[style*="background-color"], .color-picker-dropdown.color-picker-dropdown.color-picker-dropdown.color-picker-dropdown button[data-color] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
  background-attachment: scroll !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* Force color in all states */
/* .color-picker-dropdown button[style*="background-color"]:not(:hover):not(:focus):not(:active):not(:visited), .color-picker-dropdown button[data-color]:not(:hover):not(:focus):not(:active):not(:visited) {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with hover */
/* .color-picker-dropdown button[style*="background-color"]:hover, .color-picker-dropdown button[data-color]:hover {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with focus */
/* .color-picker-dropdown button[style*="background-color"]:focus, .color-picker-dropdown button[data-color]:focus {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Force color in all states with active */
/* .color-picker-dropdown button[style*="background-color"]:active, .color-picker-dropdown button[data-color]:active {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
} */

/* Final solution: Force color in all elements */
/* .color-picker-dropdown button[style*="background-color"], .color-picker-dropdown button[data-color], .color-picker-dropdown .grid button[style*="background-color"], .color-picker-dropdown .grid button[data-color], .color-picker-dropdown .grid .grid button[style*="background-color"], .color-picker-dropdown .grid .grid button[data-color] {
  background-color: var(--extracted-color, var(--data-color)) !important;
  background: var(--extracted-color, var(--data-color)) !important;
  background-image: none !important;
  background-attachment: scroll !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
  isolation: isolate !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 1 !important;
  position: relative !important;
} */

/* Exclude gradient preview from any override */
/* .color-picker-dropdown .gradient-preview, .color-picker-dropdown .gradient-preview * {
  background: unset !important;
  background-color: unset !important;
  background-image: unset !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
} */

/* Exclude the add new gradient color button from gradient background */
/* .color-picker-dropdown .add-gradient-color-btn {
  background: unset !important;
  background-color: #fff !important;
  color: #888 !important;
} */

#gradient-preview-override {
  background: var(--gradient-preview-bg, initial) !important;
  background-image: var(--gradient-preview-bg, initial) !important;
  background-color: unset !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
}

.color-picker-dropdown button,
.color-picker-dropdown button.w-full.py-2.px-4.bg-gradient-to-r.from-purple-600.to-blue-600 {
  background: linear-gradient(90deg, #7c3aed 0%, #2563eb 100%) !important;
  color: #fff !important;
  border-radius: 1rem !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  box-shadow: 0 2px 8px rgba(44,62,80,0.10) !important;
  border: none !important;
}

.color-picker-dropdown button.w-full.py-2.px-4.bg-gradient-to-r.from-purple-600.to-blue-600:hover {
  background: linear-gradient(90deg, #6d28d9 0%, #1d4ed8 100%) !important;
  color: #fff !important;
}

/* Highest specificity for the Apply Gradient button */
.color-picker-dropdown .color-picker-apply-gradient-btn.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn,
.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn {
  background: linear-gradient(90deg, #7c3aed 0%, #06b6d4 100%) !important;
  background-color: #7c3aed !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(44,62,80,0.10) !important;
  border-radius: 1rem !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  border: none !important;
  opacity: 1 !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 99999 !important;
  position: relative !important;
}

/* ===== Force color swatch buttons to show their real color even inside element-controls ===== */
.element-controls .color-picker-dropdown button[style*="background-color"] {
  background: none !important;
  background-color: unset !important;
  box-shadow: none !important;
  border: 1.5px solid #d1d5db !important;
}

.element-controls .color-picker-dropdown button[style*="background-color"]::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  background: inherit !important;
  background-color: inherit !important;
}