import React, { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { Button } from 'primereact/button';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import defaultImage from "@images/employee.svg";
import { Image } from 'primereact/image';
import axiosInstance from "../../../config/Axios";

function SettingsIndex() {
    const toast = useRef(null);
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [originalName, setOriginalName] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    // Form states
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserData();
    }, []);

    const fetchUserData = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`/users/${userId}`);
            const userData = response.data.data || response.data;

            setName(userData.name || '');
            setEmail(userData.email || '');
            setOriginalName(userData.name || '');
            setOriginalEmail(userData.email || '');
        } catch (error) {
            console.error('Error fetching user data:', error);
            showMessage('error', 'Error', 'Failed to load user data');
        }
    };

    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleUpdateProfile = async () => {
        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            console.log('Sending profile update data:', {
                name: name,
                email: email
            });

            const response = await axiosInstance.put(`/users/${userId}`, {
                name: name,
                email: email
            });

            console.log('Profile update response:', response.data);

            showMessage('success', 'Profile Updated', 'Changes saved successfully');
            setIsEditingProfile(false);
            setOriginalName(name);
            setOriginalEmail(email);
        } catch (error) {
            console.error('Error updating profile:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    const handleChangePassword = async () => {
        if (newPassword !== confirmPassword) {
            showMessage('error', 'Error', 'Passwords do not match');
            setErrors({ confirmPassword: 'Passwords do not match' });
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            // console.log('New password before sending:', newPassword);                // Don't log sensitive data please
            // console.log('New password length:', newPassword.length);

            const passwordData = {
                id: userId,
                current_password: currentPassword,
                new_password: newPassword,
                new_password_confirmation: confirmPassword
            };

            // console.log('Sending password change request with data:', passwordData);

            const response = await axiosInstance.post(`/users/change-password`, passwordData);

            console.log('Password change response:', response.data);

            showMessage('success', 'Password Changed', 'Password updated successfully');
            setIsEditingPassword(false);
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');
        } catch (error) {
            console.error('Error changing password:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update password');
        } finally {
            setLoading(false);
        }
    };

    const enterEditProfileMode = () => {
        setOriginalName(name);
        setOriginalEmail(email);
        setIsEditingProfile(true);
    };

    const cancelEditProfile = () => {
        setName(originalName);
        setEmail(originalEmail);
        setIsEditingProfile(false);
    };

    return (
        <section className='w-full flex flex-col p-5 h-[95vh] overflow-y-auto bg-white' >
            <Toast ref={toast} />

            <div className="w-full">
                {/* العنوان على أقصى اليسار */}
                <div className="mb-8 text-left">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-user bg-blue-100 rounded-full text-blue-600 text-3xl"></i>
                        Account Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Manage your personal information and security preferences</p>
                </div>

                {/* Profile Section */}
                <div className="bg-white rounded-xl shadow-md p-8 mb-8 transition-all hover:shadow-lg border border-gray-100" style={{ backgroundColor: '#ffffff' }}>
                    <div className="flex flex-col md:flex-row gap-8 mb-4">
                        <div className="flex flex-col items-center">
                            <div className="relative">
                                <Image src={defaultImage} alt="profile"
                                    imageClassName="rounded-full border-4 border-blue-50 shadow-md" width="130" height="130" />
                                {!isEditingProfile && (
                                    <Button icon="pi pi-camera" className="p-button-rounded p-button-primary absolute -bottom-2 right-0"
                                        tooltip="Change Photo" tooltipOptions={{position: 'bottom'}} />
                                )}
                            </div>
                        </div>
                        <div className="flex-1">
                            {!isEditingProfile ? (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Profile Information</h3>
                                        <Button icon="pi pi-pencil"
                                            className="p-button-rounded p-button-outlined p-button-primary"
                                            onClick={enterEditProfileMode} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Name</label>
                                            <p className="text-gray-800 font-medium">{name || 'No name provided'}</p>
                                        </div>
                                        <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                                            <p className="text-gray-800 font-medium">{email || 'No email provided'}</p>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Edit Profile</h3>
                                        <Button icon="pi pi-times"
                                            className="p-button-rounded p-button-outlined p-button-danger"
                                            onClick={cancelEditProfile} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Name</label>
                                            <InputText
                                                value={name}
                                                onChange={(e) => setName(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.name && <small className="p-error block mt-1">{errors.name}</small>}
                                        </div>
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Email</label>
                                            <InputText
                                                type="email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.email && <small className="p-error block mt-1">{errors.email}</small>}
                                        </div>
                                    </div>
                                    <div className="flex gap-3 justify-end mt-6">
                                        <Button label="Cancel" severity="secondary" className="p-button-sm"
                                            onClick={cancelEditProfile} />
                                        <Button label="Save Changes" className="p-button-sm main-btn"
                                            onClick={handleUpdateProfile}
                                            loading={loading} />
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                {/* خط فاصل على كامل عرض الصفحة */}
                <div className="w-full border-t border-gray-200 my-10 mx-0"></div>

                {/* Security Settings Header - على أقصى اليسار */}
                <div className="text-left mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-lock bg-purple-100 rounded-full text-purple-600 text-3xl"></i>
                        Security Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Protect your account with a strong password</p>
                </div>

                {/* Password Section */}
                <div className="bg-white rounded-xl shadow-md p-8 transition-all hover:shadow-lg border border-gray-100" style={{ backgroundColor: '#ffffff' }}>
                    {!isEditingPassword ? (
                        <div className="flex justify-between items-center">
                            <div className="text-left">
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Password</h3>
                                <p className="text-gray-500">Secure your account with a strong password</p>
                            </div>
                            <Button label="Change Password" icon="pi pi-lock"
                                className="p-button-outlined p-button-primary"
                                onClick={() => setIsEditingPassword(true)} />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-semibold text-gray-800">Change Password</h3>
                                <Button icon="pi pi-times"
                                    className="p-button-rounded p-button-outlined p-button-danger"
                                    onClick={() => setIsEditingPassword(false)} />
                            </div>
                            <div className="grid grid-cols-1 gap-6">
                                <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                    <label className="text-sm font-medium text-gray-700 block mb-2">Current Password</label>
                                    <Password
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        toggleMask
                                        className={`w-full ${errors.current_password ? 'p-invalid' : ''}`}
                                        inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        feedback={false}
                                    />
                                    {errors.current_password && <small className="p-error block mt-1">{errors.current_password}</small>}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                        <label className="text-sm font-medium text-gray-700 block mb-2">New Password</label>
                                        <Password
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.new_password ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.new_password && <small className="p-error block mt-1">{errors.new_password}</small>}
                                    </div>
                                    <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                        <label className="text-sm font-medium text-gray-700 block mb-2">Confirm Password</label>
                                        <Password
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.confirmPassword || (newPassword !== confirmPassword) ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.confirmPassword && <small className="p-error block mt-1">{errors.confirmPassword}</small>}
                                    </div>
                                </div>
                            </div>
                            <div className="flex gap-3 justify-end mt-6">
                                <Button label="Cancel" severity="secondary" className="p-button-sm"
                                    onClick={() => setIsEditingPassword(false)} />
                                <Button label="Update Password" className="p-button-sm main-btn"
                                    onClick={handleChangePassword}
                                    loading={loading} />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </section>
    );
}

export default SettingsIndex;
