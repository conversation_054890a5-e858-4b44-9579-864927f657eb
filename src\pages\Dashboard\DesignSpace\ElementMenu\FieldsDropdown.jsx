import { Dropdown } from 'primereact/dropdown';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { fieldsOptions } from '@constants/DesignSpaceConfig';

function FieldsDropdown() {
    const { addElement, selectedElement } = useDesignSpace();

    const onChangeHandler = (val) => {
        addElement("text", val)
    }
 
    return (
        <>
            <Dropdown
                defaultValue={fieldsOptions[0].dimension}
                className='rounded-[6px] me-3 text-[black] w-full'
                optionLabel="label"
                optionValue="value"
                value={selectedElement.value ?? ""}
                options={fieldsOptions}
                onChange={(e) => onChangeHandler(e.value)}
                placeholder="select field ..." />
        </>
    )
}

export default FieldsDropdown