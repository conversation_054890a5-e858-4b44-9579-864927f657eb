import { Route, Routes } from 'react-router-dom'

import CompanyRoutes from './CompanyRoutes';
import AdminRoutes from './AdminRoutes';

import Registration from '@pages/Auth/Registration';
import Login from '@pages/Auth/Login';
import { Auth<PERSON><PERSON><PERSON> } from './AuthChecker';

function RoutesContainer() {
  return (
    <>
      <Routes >

        {/* Auth */}
        <Route element={<AuthChecker />}>
          <Route path='/' element={<Login />} />
          <Route path='/login' element={<Login />} />
          <Route path='/register' element={<Registration />} />
          <Route path='/forget-password' element={<></>} />
          <Route path='/reset-password' element={<></>} />
        </Route>
        <Route path='/admin/*' element={<AdminRoutes />} />
        <Route path='/*' element={<CompanyRoutes />} />

        <Route path="*" element={<div>Page Not Found</div>} />
      </Routes>
    </>
  )
}

export default RoutesContainer