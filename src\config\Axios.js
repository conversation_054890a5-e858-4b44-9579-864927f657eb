import axios from 'axios';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem("token");
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }

    if (config.data instanceof FormData) {
        config.headers['Content-Type'] = 'multipart/form-data';

        // Log FormData contents for debugging
        console.log('FormData keys:', [...config.data.keys()]);
        console.log('FormData entries:', [...config.data.entries()].map(entry => {
            // Don't log large values like templates
            if (entry[0] === 'template' && typeof entry[1] === 'string' && entry[1].length > 100) {
                return [entry[0], `${entry[1].substring(0, 100)}... (${entry[1].length} chars)`];
            }
            return entry;
        }));
    } else {
        config.headers['Content-Type'] = 'application/json';

        // Log request data for debugging
        if (config.data && typeof config.data === 'object') {
            const dataCopy = {...config.data};

            // Don't log large values like templates
            if (dataCopy.template && typeof dataCopy.template === 'string' && dataCopy.template.length > 100) {
                dataCopy.template = `${dataCopy.template.substring(0, 100)}... (${dataCopy.template.length} chars)`;
            }

            console.log('Request data:', dataCopy);
        }
    }

    config.headers['Accept'] = 'application/json';

    console.log('Request config:', {
        url: config.url,
        method: config.method,
        headers: config.headers,
        hasData: !!config.data
    });

    return config;
});

axiosInstance.defaults.baseURL = import.meta.env.VITE_BACKEND_URL;

export default axiosInstance;

axiosInstance.interceptors.response.use(
    (response) => {
        // Log response for debugging
        console.log('Response from:', response.config.url, {
            status: response.status,
            statusText: response.statusText,
            data: response.data
        });

        // Skip redirects for registration endpoint
        if (response.config.url.includes('/register')) {
            return response;
        }

        if (response.config.url.includes('/packages/purchase/') && response.status === 200) {
            const sessionId = response.data.sessionId;
            if (sessionId) {
                window.location.href = `https://checkout.stripe.com/pay/${sessionId}`;
                return;
            }
        }

        if (response.data.user && response.data.user.user_type) {
            const userType = response.data.user.user_type;
            localStorage.setItem("user_role", userType);
            localStorage.setItem("user_type", userType);

            switch(userType) {
                case 'user':
                    window.location.href = '/users/dashboard';
                    break;
                case 'admin':
                    window.location.href = '/admin/dashboard';
                    break;
                case 'manager':
                    window.location.href = '/users/members';
                    break;
                default:
                    window.location.href = '/login';
            }
        }

        return response;
    },
    (error) => {
        console.error('API Error:', {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message
        });

        const token = localStorage.getItem("token");
        if (error.response && (error.response.status === 401 || error.response.status === 403) && token) {
            localStorage.clear();
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);
