import { useEffect } from 'react'
import { useF<PERSON>, Controller } from "react-hook-form";
import { useParams } from 'react-router-dom';

import { useCreateTemplateMutation, useUpdateTemplateMutation } from '@quires';
import { getFormErrorMessage } from '@utils/helper'
import { useGlobalContext } from '@contexts/GlobalContext';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import axiosInstance from '../../../../config/Axios';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';


function CreationDialog({ formData, onSuccess }) {
    const { control, formState: { errors }, handleSubmit, reset } = useForm();
    const { openDialog, dialogHandler, disableBtn } = useGlobalContext();
    const { cardType, elements, canvasBackground, canvasBackgroundStyle } = useDesignSpace();

    const createTemplate = useCreateTemplateMutation();
    const updateTemplate = useUpdateTemplateMutation();

    const { id } = useParams();

    // Log formData when component renders or formData changes
    console.log("CreationDialog received formData:", {
        hasHtmlTemplate: !!formData?.htmlTemplate,
        htmlTemplateLength: formData?.htmlTemplate?.length || 0,
        hasName: !!formData?.name,
        name: formData?.name,
        cardTypeId: cardType?.id
    });

    const formHandler = async (data) => id ? updateTemplateHandler(data) : createTemplateHandler(data)

    const updateTemplateHandler = async (data) => {
        // Use formData.htmlTemplate which should be set by the parent component
        const templateContent = formData?.htmlTemplate;

        // Log detailed information about the data being sent
        console.log("Updating template with data:", {
            id: id,
            name: data?.name,
            templateContentLength: templateContent ? templateContent.length : 0,
            elementsCount: elements ? elements.length : 0,
            cardTypeId: cardType?.id
        });

        // Log warning but continue with the save operation
        if (!templateContent) {
            console.warn("Warning: No template content available, but proceeding with update operation");
        }

        try {
            // Create a default template if none is provided
            const defaultTemplate = `
                <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f0f0f0;">
                    <div style="text-align: center; padding: 20px; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h2 style="margin-bottom: 10px; color: #333;">${data?.name || 'Updated Design'}</h2>
                        <p style="color: #666;">Updated on ${new Date().toLocaleDateString()}</p>
                    </div>
                </div>
            `;

            // Ensure background and background_style are properly formatted
            // Use values from the component scope (already retrieved at component level)

            // Get the current background from the DOM directly
            let actualBackground = '';
            let actualBackgroundStyle = null;

            // Try to get the background from the DOM first
            const designSpaceContent = document.getElementById('design-space-content');
            if (designSpaceContent) {
                const computedStyle = window.getComputedStyle(designSpaceContent);
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;

                console.log("Actual computed background from DOM in updateTemplateHandler:", {
                    backgroundColor: bgColor,
                    backgroundImage: bgImage
                });

                // If background is a solid color (not "none" or "rgba(0, 0, 0, 0)")
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                    console.log("Using solid color from DOM in updateTemplateHandler:", actualBackground);
                }
                // If background is a gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    console.log("Using gradient/image from DOM in updateTemplateHandler:", actualBackground);

                    // Get additional background properties if needed
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }
            }

            // Use the DOM-extracted background or fall back to context/formData values
            const background = actualBackground || canvasBackground || formData?.background || '';
            const backgroundStyle = actualBackgroundStyle ?
                JSON.stringify(actualBackgroundStyle) :
                (canvasBackgroundStyle ?
                    (typeof canvasBackgroundStyle === 'string' ?
                        canvasBackgroundStyle :
                        JSON.stringify(canvasBackgroundStyle)) :
                    formData?.backgroundStyle || '');

            // Log all the background values
            console.log("All background values for update:", {
                fromDOM: {
                    background: actualBackground,
                    backgroundStyle: actualBackgroundStyle
                },
                fromContext: {
                    background: canvasBackground,
                    backgroundStyle: canvasBackgroundStyle
                },
                fromFormData: {
                    background: formData?.background,
                    backgroundStyle: formData?.backgroundStyle
                },
                final: {
                    background: background,
                    backgroundStyle: backgroundStyle,
                    isSolidColor: background && (background.startsWith('#') || background.startsWith('rgb'))
                }
            });

            console.log("Background data for updating:", {
                fromContext: {
                    background: canvasBackground,
                    backgroundStyle: canvasBackgroundStyle
                },
                fromFormData: {
                    background: formData?.background,
                    backgroundStyle: formData?.backgroundStyle
                },
                final: {
                    background: background,
                    backgroundStyle: backgroundStyle
                }
            });

            const _formData = new FormData()
            _formData.append("id", id)
            _formData.append("name", data?.name)
            _formData.append("template", templateContent || defaultTemplate)
            _formData.append("card_type_id", cardType?.id)
            _formData.append("init_template", JSON.stringify(elements || []))
            _formData.append("background", background)
            _formData.append("background_style", backgroundStyle)
            _formData.append("_method", "PUT")

            // Log all form data entries to verify background data is included
            console.log("FormData entries:");
            for (let [key, value] of _formData.entries()) {
                console.log(`${key}: ${value.substring ? (value.length > 100 ? value.substring(0, 100) + '...' : value) : '[non-string value]'}`);
            }

            console.log("FormData prepared for update. Keys:", [..._formData.keys()]);

            // Verify template content is not empty
            const templateCheck = _formData.get('template');
            console.log("Template content check:", {
                isEmpty: !templateCheck,
                length: templateCheck ? templateCheck.length : 0,
                preview: templateCheck ? templateCheck.substring(0, 100) + '...' : 'EMPTY',
                background: _formData.get('background'),
                backgroundStyle: _formData.get('background_style')
            });

            await updateTemplate.mutateAsync({
                data: _formData,
                id: id
            }, {
                onSuccess: (response) => {
                    console.log("Template updated successfully:", response);
                    reset();

                    // Close the template name dialog first
                    dialogHandler("createDesignTemplate");

                    // Wait a short moment before showing the image generation modal
                    // This ensures the first modal is fully closed before the second one appears
                    setTimeout(() => {
                        // Show success message and trigger image generation modal
                        if (onSuccess && typeof onSuccess === 'function') {
                            onSuccess();
                        }
                    }, 300);
                },
                onError: (error) => {
                    console.error("Error updating template:", error);

                    // Show error message
                }
            });
        } catch (error) {
            console.error("Exception during template update:", error);

            // Show error message

        }
    }

    const createTemplateHandler = async (data) => {
        // Use formData.htmlTemplate which should be set by the parent component
        const templateContent = formData?.htmlTemplate;

        // Log detailed information about the data being sent
        console.log("Creating template with data:", {
            name: data?.name,
            templateContentLength: templateContent ? templateContent.length : 0,
            elementsCount: elements ? elements.length : 0,
            cardTypeId: cardType?.id
        });

        // Log warning but continue with the save operation
        if (!templateContent) {
            console.warn("Warning: No template content available, but proceeding with save operation");
        }

        try {
            // Create a default template if none is provided
            const defaultTemplate = `
                <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f0f0f0;">
                    <div style="text-align: center; padding: 20px; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h2 style="margin-bottom: 10px; color: #333;">New Design Template</h2>
                        <p style="color: #666;">Created on ${new Date().toLocaleDateString()}</p>
                    </div>
                </div>
            `;

            // Ensure background and background_style are properly formatted
            // Use values from the component scope (already retrieved at component level)

            // Get the current background from the DOM directly
            let actualBackground = '';
            let actualBackgroundStyle = null;

            // Try to get the background from the DOM first
            const designSpaceContent = document.getElementById('design-space-content');
            if (designSpaceContent) {
                const computedStyle = window.getComputedStyle(designSpaceContent);
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;

                console.log("Actual computed background from DOM in CreationDialog:", {
                    backgroundColor: bgColor,
                    backgroundImage: bgImage
                });

                // If background is a solid color (not "none" or "rgba(0, 0, 0, 0)")
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                    console.log("Using solid color from DOM in CreationDialog:", actualBackground);
                }
                // If background is a gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    console.log("Using gradient/image from DOM in CreationDialog:", actualBackground);

                    // Get additional background properties if needed
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }
            }

            // Use the DOM-extracted background or fall back to context/formData values
            const background = actualBackground || canvasBackground || formData?.background || '';
            const backgroundStyle = actualBackgroundStyle ?
                JSON.stringify(actualBackgroundStyle) :
                (canvasBackgroundStyle ?
                    (typeof canvasBackgroundStyle === 'string' ?
                        canvasBackgroundStyle :
                        JSON.stringify(canvasBackgroundStyle)) :
                    formData?.backgroundStyle || '');

            // Log all the background values
            console.log("All background values for create:", {
                fromDOM: {
                    background: actualBackground,
                    backgroundStyle: actualBackgroundStyle
                },
                fromContext: {
                    background: canvasBackground,
                    backgroundStyle: canvasBackgroundStyle
                },
                fromFormData: {
                    background: formData?.background,
                    backgroundStyle: formData?.backgroundStyle
                },
                final: {
                    background: background,
                    backgroundStyle: backgroundStyle,
                    isSolidColor: background && (background.startsWith('#') || background.startsWith('rgb'))
                }
            });

            console.log("Background data for saving:", {
                fromContext: {
                    background: canvasBackground,
                    backgroundStyle: canvasBackgroundStyle
                },
                fromFormData: {
                    background: formData?.background,
                    backgroundStyle: formData?.backgroundStyle
                },
                final: {
                    background: background,
                    backgroundStyle: backgroundStyle
                }
            });

            // Create payload with the background data
            const payload = {
                name: data?.name,
                template: templateContent || defaultTemplate,
                init_template: JSON.stringify(elements || []),
                card_type_id: cardType?.id,
                background: background,
                background_style: backgroundStyle
            };

            // Log the final payload to verify background data is included
            console.log("Final payload with background data:", {
                name: payload.name,
                background: payload.background,
                background_style: payload.background_style
            });

            console.log("Sending payload to server:", {
                name: payload.name,
                templateLength: payload.template.length,
                initTemplateLength: payload.init_template.length,
                cardTypeId: payload.card_type_id,
                background: payload.background,
                backgroundStyle: payload.background_style
            });

            await createTemplate.mutateAsync(payload, {
                onSuccess: (response) => {
                    console.log("Template created successfully:", response);
                    reset();

                    // Close the template name dialog first
                    dialogHandler("createDesignTemplate");

                    // Wait a short moment before showing the image generation modal
                    // This ensures the first modal is fully closed before the second one appears
                    setTimeout(() => {
                        // Show success message and trigger image generation modal
                        if (onSuccess && typeof onSuccess === 'function') {
                            // Pass the new design ID to the onSuccess callback
                            console.log("Passing design ID to parent:", response.id);

                            // No need to set status manually, it's handled by the backend
                            console.log("FINAL STEP: Passing design ID to parent:", response.id);
                            onSuccess(response.id);
                        }
                    }, 300);
                },
                onError: (error) => {
                    console.error("Error creating template:", error);

                    // Show error message

                }
            });
        } catch (error) {
            console.error("Exception during template creation:", error);

            // Show error message

        }
    }

    useEffect(() => {
        if (id)
            reset({ name: formData?.name })
    }, [id, reset])

    return (
        <Dialog visible={openDialog.createDesignTemplate}
            style={{ width: '50vw' }}
            breakpoints={{ '960px': '95vw' }}
            header={"Template Name"}
            modal className="p-fluid"
            onHide={() => dialogHandler("createDesignTemplate")}
        >
            <form onSubmit={handleSubmit(formHandler)} className="w-full flex flex-col justify-center">
                <div className="col-full flex flex-wrap  justify-start py-4 border-[gray]">
                    {/* Name */}
                    <div className='w-full mb-3 px-2'>
                        <div className="field ">
                            <label className="form-label text-md ">Design Template Name </label>
                            <span className="p-float-label mt-2">
                                <Controller name="name" control={control}
                                    rules={{ required: 'Name is required.' }}
                                    render={({ field, fieldState }) => (
                                        <InputText
                                            id={field.name}
                                            {...field}
                                            autoFocus
                                            inputRef={field.ref}
                                            className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                        />
                                    )} />
                            </span>
                            {getFormErrorMessage('name', errors)}
                        </div>
                    </div>
                </div>

                <div className="col-full text-center flex items-end justify-start px-24 py-4">
                    <Button
                        label="Cancel"
                        aria-label="Close"
                        type="reset"
                        className="gray-btn w-3/12  me-2 text-center"
                        disabled={disableBtn || createTemplate.isLoading}
                        data-bs-dismiss="modal"
                        onClick={() => dialogHandler("createDesignTemplate")} />

                    <Button
                        label="Save"
                        aria-label="Add"
                        type="submit"
                        className="main-btn w-auto  ms-2 text-center"
                        disabled={disableBtn}
                        loading={createTemplate.isLoading}
                    />

                </div>
            </form>
        </Dialog>)
}

export default CreationDialog