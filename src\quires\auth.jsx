import _ from "lodash";
import { useMutation } from "react-query"
import { useNavigate } from "react-router-dom";

import axiosInstance from '../config/Axios';
// import { emptyLocalStorage, setProfile } from "../config/global";
import { useGlobalContext } from "@contexts/GlobalContext"
import { handleErrors } from "../utils/helper";

// Login

const login = async (payload) => {
    const { data } = await axiosInstance.post("/login", payload);

    return data;
}

export const useLogInMutation = () => {
    const { showToast, userType, setUserType } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(login, {
        onSuccess: async (data) => {
            const type = data?.user?.user_type || "company"
            localStorage.setItem("email", data?.user?.email);
            localStorage.setItem("token", data?.token);
            localStorage.setItem("user_id", data?.user?.id);
            localStorage.setItem("user_type", type);
            localStorage.setItem("user_name", data?.user?.name)
            setUserType(type)
            navigate("/users/members")
        },
        onError: () => showToast("error", "Login Credential", "Invalid Username Or Password!")
    })
}

// Registration
const register = async (payload) => {
    const { data } = await axiosInstance.post("/register", payload);

    return data;
}

export const useRegisterMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(register, {
        onSuccess: async (data) => {
            // localStorage.setItem("email", data?.user?.email);
            // localStorage.setItem("user_name", data?.user?.name)
            // localStorage.setItem("user_type", type);
            // localStorage.setItem("token", data?.token);
            // localStorage.setItem("user_id", data?.user?.id);
            navigate("/login")
        },
        onError: (error) => {
            handleErrors(showToast, error)
        }
    })
}

// Verify Email
const verifyEmail = async (payload) => {
    const { data } = await axiosInstance.post("/users/verify/email", payload);

    return data.data;
}

export const useVerifyEmailMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(verifyEmail, {
        onSuccess: async (data) => {
            setProfile(data)
            showToast('success', 'Email Verification', "Email has been verified successfully!");
            navigate("/dashboard")
        },
        onError: (error) => {
            navigate('/login')
            showToast("error", "Email Verification", error?.response?.data?.message)
        }
    })
}

// Resend Verification Email
const resendEmail = async (payload) => {
    const { data } = await axiosInstance.post("users/resend/email/verification", payload);

    return data.data;
}

export const useResendEmailMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(resendEmail, {
        onSuccess: async (data) => {
            localStorage.setItem("verification_attempts", data?.verification_attempts);
            showToast("success", "Email Verification", data?.msg);
        },
        onError: (error) => {
            let _data = error?.response?.data;
            localStorage.setItem("verification_attempts", _data?.verification_attempts);
            showToast("error", "Email Verification", _data?.msg)
        }
    })
}

// Forget Password
const forgetPassword = async (payload) => {
    const { data } = await axiosInstance.post("/users/forget-password", payload);

    return data.data;
}

export const useForgetPasswordMutation = () => {
    const { showToast } = useGlobalContext();

    return useMutation(forgetPassword, {
        onSuccess: async (data) => {
            showToast('success', 'Forget Password', data?.msg);

        },
        onError: (error) => {
            showToast('error', 'Forget Password', error?.response?.data?.message);
        }
    })
}

// Reset Password
const resetPassword = async (payload) => {
    const { data } = await axiosInstance.post(`/users/reset-password/${payload?.token}`, payload.data)

    return data.data;
}

export const useResetPasswordMutation = () => {
    const { showToast } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(resetPassword, {
        onSuccess: async (data) => {
            showToast('success', 'Reset Password', data?.msg);
            navigate("/login");
        },
        onError: (error) => {
            showToast('error', 'Reset Password', error.response?.data?.message);
        }
    })
}

// Logout
const logout = async () => {
    const { data } = await axiosInstance.post(`/logout`)

    return data.data;
}

export const useLogoutMutation = () => {
    const { showToast, setUserType } = useGlobalContext();
    const navigate = useNavigate();

    return useMutation(logout, {
        onSuccess: async (data) => {
            // Clear all localStorage items
            localStorage.clear();
            // Use window.location for a full page reload to ensure clean state thus no white page . Yaay
                window.location.href = "/login";
            showToast('success', 'Logout', data?.message);
        },
        onError: (error) => {
            // Even on error, try to logout the user
            localStorage.clear();
            setTimeout(() => {
                window.location.href = "/login";
            }, 100);

            showToast('error', 'Logout', error.response?.data?.message || 'Logout failed');
        }
    })
}