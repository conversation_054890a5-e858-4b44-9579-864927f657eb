import React from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { getFormErrorMessage } from '@utils/helper'
import { useLogInMutation } from '@quires';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import SideImage from './SideImage';

function Login () {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control } = useForm();

  const login = useLogInMutation()

  const onSubmit = async (data) => {
    try {
      const response = await login.mutateAsync(data);
      console.log(response);
    } catch (error) {
      console.error("Login error:", error.response?.data || error.message);
    }
  };
  

  
  return (
    <div className='w-full h-[100vh] overflow-hidden flex'>
      <SideImage />
      <div className='w-full sm:w-7/12 h-full px-12 flex flex-col justify-center '>
        <h1 className='text-3xl font-bold mb-12'>{ t('login') }</h1>
        <form onSubmit={ handleSubmit(onSubmit) } className="flex flex-col">

          {/* email */ }
          <div className="mb-2 w-full">
            <div className="field">
              <label className="form-label mb-2 text-[#696F79]">{ t('inputs.email') }</label>
              <span className="p-float-label mt-2">
                <Controller name="email" control={ control }
                  rules={ {
                    required: t('messages.required'),
                    pattern: {
                      value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                      message: t('messages.email'),
                    }
                  } }
                  render={ ({ field, fieldState }) => (
                    <InputText
                      id={ field.email }
                      { ...field }
                      inputRef={ field.ref }
                      placeholder={ t('placeholders.email') }
                      className={ `w-full text-[#696F79] p-3 ${ classNames({ 'p-invalid': fieldState.invalid }) }` } />
                  ) } />
              </span>
              { getFormErrorMessage('email', errors) }
            </div>
          </div>

          {/* password */ }
          <div className="mb-2 form-password-toggle w-full my-4">
            <div className="field ">
              <div className="flex justify-between">
                <label className="form-label mb-2 text-[#696F79]" htmlFor="password"> { t('inputs.password') } </label>
                <Link to="/forget/password">
                  <small className='text-[#696F79]'>{ t('go_to.forget_pass') }</small>
                </Link>
              </div>
              <span className="p-float-label">
                <Controller name="password" control={ control }
                  rules={ { required: t('messages.required') } }
                  render={ ({ field, fieldState }) => (
                    <Password
                      id={ field.password }
                      { ...field }
                      inputRef={ field.ref }
                      placeholder={ t('placeholders.password') }
                      className={ `text-[#696F79] pass-input w-full ${ classNames({ 'p-invalid': fieldState.invalid }) }` }
                      toggleMask
                      feedback={false} />
                  ) } />
              </span>
              { getFormErrorMessage('password', errors) }
            </div>
          </div>

          <button className="main-btn w-full mt-8 text-md sm:text-xl"> { t('login') } </button>
        </form>

        <p className="mt-3 text-[#696F79] text-sm">
          { t('go_to.register') }
          <Link to="/register">
            <span className="mx-1 capitalize text-[#427bf0]">{ t('register') }</span>
          </Link>
        </p>
      </div>
    </div>
  )
}

export default Login